// Standalone Liquid Ether background (vanilla Three.js)
// Replaces React/ESM code so it runs on a plain HTML page.
// Requires three.min.js (added via CDN in index.html)

(function(){
  const prefersReduced = window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  document.addEventListener('DOMContentLoaded', () => {
    const container = document.getElementById('liquid-ether');
    if (!container || typeof THREE === 'undefined') return;

    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true, powerPreference: 'high-performance' });
    renderer.setPixelRatio(Math.min(window.devicePixelRatio || 1, 2));
    container.appendChild(renderer.domElement);

    const scene = new THREE.Scene();
    const camera = new THREE.Camera();
    const geometry = new THREE.PlaneGeometry(2, 2);

    const vertexShader = `
      precision highp float;
      attribute vec3 position;
      void main(){ gl_Position = vec4(position, 1.0); }
    `;

    const fragmentShader = `
      precision highp float;
      uniform vec3 iResolution; // (w,h,1)
      uniform float iTime;

      vec2 hash2(vec2 p){
        p = vec2(dot(p, vec2(127.1,311.7)), dot(p, vec2(269.5,183.3)));
        return -1.0 + 2.0*fract(sin(p)*43758.5453123);
      }
      float noise(vec2 p){
        vec2 i = floor(p);
        vec2 f = fract(p);
        vec2 u = f*f*(3.0-2.0*f);
        float n00 = dot(hash2(i+vec2(0.0,0.0)), f-vec2(0.0,0.0));
        float n10 = dot(hash2(i+vec2(1.0,0.0)), f-vec2(1.0,0.0));
        float n01 = dot(hash2(i+vec2(0.0,1.0)), f-vec2(0.0,1.0));
        float n11 = dot(hash2(i+vec2(1.0,1.0)), f-vec2(1.0,1.0));
        float nx0 = mix(n00, n10, u.x);
        float nx1 = mix(n01, n11, u.x);
        return mix(nx0, nx1, u.y);
      }
      float fbm(vec2 p){
        float v = 0.0; float a = 0.5;
        for(int i=0;i<5;i++){ v += a * noise(p); p = p*2.0 + 0.33; a *= 0.5; }
        return v;
      }
      vec3 palette(float t){
        vec3 c1 = vec3(0.06, 0.09, 0.25);
        vec3 c2 = vec3(0.16, 0.08, 0.38);
        vec3 c3 = vec3(0.42, 0.16, 0.72);
        vec3 c4 = vec3(0.98, 0.62, 0.90);
        float m1 = smoothstep(0.0, 0.5, t);
        float m2 = smoothstep(0.3, 1.0, t);
        vec3 a = mix(c1, c2, m1);
        vec3 b = mix(c3, c4, m2);
        return mix(a, b, smoothstep(0.2, 0.9, t));
      }
      void main(){
        vec2 uv = gl_FragCoord.xy / iResolution.xy;
        vec2 p = (2.0*uv - 1.0);
        p.x *= iResolution.x / iResolution.y;
        float t = iTime * 0.12;
        vec2 q = vec2(
          fbm(p*1.2 + vec2(t*0.9, -t*0.7)),
          fbm(p*1.2 + vec2(-t*0.6, t*0.8))
        );
        vec2 r = vec2(
          fbm(p*3.0 + 2.0*q + vec2(t*0.3, -t*0.25)),
          fbm(p*3.0 + 2.0*q + vec2(-t*0.22, t*0.27))
        );
        float n = fbm(p*2.5 + 2.5*r);
        vec3 col = palette(n);
        float d = length(p);
        col *= 1.0 - 0.15*smoothstep(0.6, 1.1, d);
        gl_FragColor = vec4(col, 1.0);
      }
    `;

    const uniforms = {
      iResolution: { value: new THREE.Vector3(1, 1, 1) },
      iTime: { value: 0 }
    };

    const material = new THREE.ShaderMaterial({
      vertexShader,
      fragmentShader,
      uniforms,
      transparent: true,
      depthWrite: false
    });

    const mesh = new THREE.Mesh(geometry, material);
    scene.add(mesh);

    function resize(){
      const w = window.innerWidth, h = window.innerHeight;
      renderer.setSize(w, h);
      uniforms.iResolution.value.set(w, h, 1);
    }
    window.addEventListener('resize', resize);
    resize();

    let raf = null; let start = performance.now();
    function tick(){
      if (prefersReduced) return;
      uniforms.iTime.value = (performance.now() - start) / 1000;
      renderer.render(scene, camera);
      raf = requestAnimationFrame(tick);
    }
    if (!prefersReduced) tick();

    window.addEventListener('pagehide', () => {
      if (raf) cancelAnimationFrame(raf);
      try { renderer.dispose(); } catch(e){}
    }, { once: true });
  });
})();
